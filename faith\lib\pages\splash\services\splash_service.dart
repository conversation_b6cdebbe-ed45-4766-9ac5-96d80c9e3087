import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/splash_config.dart';

/// Splash 配置服务
/// 负责从后台获取启动页面的配置信息
class SplashService extends GetxService {
  static SplashService get to => Get.find();

  /// 当前配置
  final _config = SplashConfig.defaultConfig.obs;
  SplashConfig get config => _config.value;

  /// 是否正在加载配置
  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    // 应用启动时加载配置
    loadConfig();
  }

  /// 从后台加载配置
  Future<void> loadConfig() async {
    try {
      _isLoading.value = true;
      
      // 模拟从后台 API 获取配置
      // 实际项目中这里应该是真实的 API 调用
      final configData = await _fetchConfigFromApi();
      
      if (configData != null) {
        _config.value = SplashConfig.fromJson(configData);
      }
    } catch (e) {
      // 加载失败时使用默认配置
      debugPrint('加载 Splash 配置失败: $e');
      _config.value = SplashConfig.defaultConfig;
    } finally {
      _isLoading.value = false;
    }
  }

  /// 模拟从 API 获取配置数据
  /// 实际项目中替换为真实的 HTTP 请求
  Future<Map<String, dynamic>?> _fetchConfigFromApi() async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));
    
    // 模拟后台返回的配置数据
    // 实际项目中这里应该是 HTTP 请求
    return {
      'app_name': 'Faith',
      'subtitle': '简约 · 优雅 · 现代',
      'background_image_url': null, // 可以设置网络图片 URL
      'background_image_asset': '../../../assets/splash.png', // 可以设置本地资源路径
      'display_duration': 5,
      'show_skip_button': true,
      'version': '1.0.0',
      'copyright': '© 2024 Faith App',
    };
  }

  /// 更新配置（用于测试或动态更新）
  void updateConfig(SplashConfig newConfig) {
    _config.value = newConfig;
  }

  /// 设置背景图片 URL
  void setBackgroundImageUrl(String url) {
    _config.value = _config.value.copyWith(backgroundImageUrl: url);
  }

  /// 设置应用名称
  void setAppName(String name) {
    _config.value = _config.value.copyWith(appName: name);
  }

  /// 设置副标题
  void setSubtitle(String subtitle) {
    _config.value = _config.value.copyWith(subtitle: subtitle);
  }

  /// 获取一些预设的配置示例
  static List<SplashConfig> getPresetConfigs() {
    return [
      // 默认配置
      SplashConfig.defaultConfig,
      
      // 自然风景主题
      const SplashConfig(
        appName: 'Faith',
        subtitle: '探索 · 发现 · 成长',
        backgroundImageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
        displayDuration: 5,
      ),
      
      // 科技主题
      const SplashConfig(
        appName: 'Faith',
        subtitle: '创新 · 科技 · 未来',
        backgroundImageUrl: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176',
        displayDuration: 5,
      ),
      
      // 简约主题
      const SplashConfig(
        appName: 'Faith',
        subtitle: '简约 · 纯净 · 专注',
        displayDuration: 3,
      ),
    ];
  }
}

/// Splash 配置扩展方法
extension SplashConfigExtension on SplashConfig {
  /// 是否有背景图片
  bool get hasBackgroundImage => 
      (backgroundImageUrl != null && backgroundImageUrl!.isNotEmpty) ||
      (backgroundImageAsset != null && backgroundImageAsset!.isNotEmpty);
  
  /// 获取背景图片类型
  String get backgroundImageType {
    if (backgroundImageUrl != null && backgroundImageUrl!.isNotEmpty) {
      return 'network';
    } else if (backgroundImageAsset != null && backgroundImageAsset!.isNotEmpty) {
      return 'asset';
    } else {
      return 'gradient';
    }
  }
}
