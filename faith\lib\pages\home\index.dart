import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:faith/theme/theme_controller.dart';
import 'package:get/get.dart';

class HomeIndex extends StatefulWidget {
  const HomeIndex({super.key});

  @override
  State<HomeIndex> createState() => _HomeIndexState();
}

class _HomeIndexState extends State<HomeIndex>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeController>(
      builder: (themeController) => Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text('ShadcnUI 组件演示', style: ShadTheme.of(context).textTheme.h3),
              const SizedBox(height: 16),

              // 卡片组件
              ShadCard(
                title: const Text('欢迎使用 Faith'),
                description: const Text('这是一个使用 ShadcnUI 主题的 Flutter 应用'),
                child: Column(
                  children: [
                    const SizedBox(height: 16),

                    // 按钮组件
                    Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: ShadButton(
                                onPressed: () {
                                  Get.toNamed('/theme-settings');
                                },
                                child: const Text('主题设置'),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: ShadButton.outline(
                                onPressed: () {
                                  // 切换主题
                                  ThemeController.to.toggleThemeMode();
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        ThemeController.to.isDarkMode
                                            ? '已切换到暗色主题'
                                            : '已切换到亮色主题',
                                      ),
                                    ),
                                  );
                                },
                                child: GetBuilder<ThemeController>(
                                  builder: (controller) =>
                                      Text(controller.isDarkMode ? '切换到亮色' : '切换到暗色'),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        ShadButton.outline(
                          onPressed: () {
                            Navigator.pushNamed(context, '/two');
                          },
                          child: const Text('测试404页面'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // 输入框组件
                    const ShadInput(placeholder: Text('请输入内容...')),
                    const SizedBox(height: 16),

                    // 主题状态显示
                    GetBuilder<ThemeController>(
                      builder: (controller) => Column(
                        children: [
                          Row(
                            children: [
                              Text('显示模式: ${controller.isDarkMode ? "暗色" : "亮色"}'),
                              const Spacer(),
                              ShadSwitch(
                                value: controller.isDarkMode,
                                onChanged: (value) {
                                  controller.setThemeMode(value);
                                },
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Text('颜色主题: ${controller.currentThemeDisplayName}'),
                              const Spacer(),
                              Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color: controller.currentThemeColor.seedColor,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Theme.of(context).colorScheme.outline,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 通知开关组件
                    Row(
                      children: [
                        const Text('启用通知'),
                        const Spacer(),
                        ShadSwitch(
                          value: true,
                          onChanged: (value) {
                            // 处理开关状态
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // 徽章组件
              Wrap(
                spacing: 8,
                children: [
                  ShadBadge(child: const Text('Primary')),
                  ShadBadge.secondary(child: const Text('Secondary')),
                  ShadBadge.destructive(child: const Text('Destructive')),
                  ShadBadge.outline(child: const Text('Outline')),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
