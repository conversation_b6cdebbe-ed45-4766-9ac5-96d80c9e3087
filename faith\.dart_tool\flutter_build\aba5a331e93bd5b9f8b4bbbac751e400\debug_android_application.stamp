{"inputs": ["E:\\code\\cool-uniapp\\faith\\.dart_tool\\flutter_build\\aba5a331e93bd5b9f8b4bbbac751e400\\app.dill", "D:\\environment\\Flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "D:\\environment\\Flutter\\bin\\cache\\engine.stamp", "D:\\environment\\Flutter\\bin\\cache\\engine.stamp", "D:\\environment\\Flutter\\bin\\cache\\engine.stamp", "D:\\environment\\Flutter\\bin\\cache\\engine.stamp", "E:\\code\\cool-uniapp\\faith\\pubspec.yaml", "E:\\code\\cool-uniapp\\faith\\assets\\svg\\404.svg", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lucide_icons_flutter-3.0.6\\assets\\lucide.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lucide_icons_flutter-3.0.6\\assets\\build_font\\LucideVariable-w100.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lucide_icons_flutter-3.0.6\\assets\\build_font\\LucideVariable-w200.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lucide_icons_flutter-3.0.6\\assets\\build_font\\LucideVariable-w300.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lucide_icons_flutter-3.0.6\\assets\\build_font\\LucideVariable-w400.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lucide_icons_flutter-3.0.6\\assets\\build_font\\LucideVariable-w500.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lucide_icons_flutter-3.0.6\\assets\\build_font\\LucideVariable-w600.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\Geist-Thin.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\Geist-UltraLight.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\Geist-Light.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\Geist-Regular.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\Geist-Medium.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\Geist-SemiBold.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\Geist-Bold.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\Geist-Black.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\Geist-UltraBlack.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\GeistMono-Thin.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\GeistMono-UltraLight.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\GeistMono-Light.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\GeistMono-Regular.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\GeistMono-Medium.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\GeistMono-SemiBold.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\GeistMono-Bold.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\GeistMono-Black.otf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\fonts\\GeistMono-UltraBlack.otf", "D:\\environment\\Flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "D:\\environment\\Flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "E:\\code\\cool-uniapp\\faith\\.dart_tool\\flutter_build\\aba5a331e93bd5b9f8b4bbbac751e400\\native_assets.json", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\args-2.7.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.13.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boxy-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cookie_jar-4.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio-5.8.0+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio_cookie_manager-3.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio_http2_adapter-2.6.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio_web_adapter-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\extended_image-10.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\extended_image_library-5.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_animate-4.5.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_screenutil-5.9.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_shaders-0.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_svg-2.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get-4.7.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\get_storage-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http2-2.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_client_helper-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.20.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\js-0.7.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lucide_icons_flutter-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_parsing-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pull_to_refresh-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shadcn_ui-0.28.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\two_dimensional_scrollables-0.3.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\universal_image-1.0.10\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\universal_io-2.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics-1.1.19\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics_codec-1.1.13\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics_compiler-1.1.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE", "D:\\environment\\Flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "D:\\environment\\Flutter\\packages\\flutter\\LICENSE", "E:\\code\\cool-uniapp\\faith\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD149009841"], "outputs": ["E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\svg\\404.svg", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\lucide_icons_flutter\\assets\\lucide.ttf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\lucide_icons_flutter\\assets\\build_font\\LucideVariable-w100.ttf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\lucide_icons_flutter\\assets\\build_font\\LucideVariable-w200.ttf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\lucide_icons_flutter\\assets\\build_font\\LucideVariable-w300.ttf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\lucide_icons_flutter\\assets\\build_font\\LucideVariable-w400.ttf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\lucide_icons_flutter\\assets\\build_font\\LucideVariable-w500.ttf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\lucide_icons_flutter\\assets\\build_font\\LucideVariable-w600.ttf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\Geist-Thin.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\Geist-UltraLight.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\Geist-Light.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\Geist-Regular.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\Geist-Medium.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\Geist-SemiBold.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\Geist-Bold.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\Geist-Black.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\Geist-UltraBlack.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\GeistMono-Thin.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\GeistMono-UltraLight.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\GeistMono-Light.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\GeistMono-Regular.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\GeistMono-Medium.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\GeistMono-SemiBold.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\GeistMono-Bold.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\GeistMono-Black.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\shadcn_ui\\fonts\\GeistMono-UltraBlack.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "E:\\code\\cool-uniapp\\faith\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]}