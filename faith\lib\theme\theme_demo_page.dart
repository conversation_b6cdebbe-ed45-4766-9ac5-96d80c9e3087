import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'theme_controller.dart';
import 'theme_extensions.dart';
import 'colors.dart';

/// 主题演示页面
/// 展示自定义主题颜色和组件效果
class ThemeDemoPage extends StatelessWidget {
  const ThemeDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeController>(
      builder: (controller) => Scaffold(
        appBar: AppBar(
          title: const Text('主题演示'),
          backgroundColor: context.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 当前主题信息
              _buildCurrentThemeInfo(controller),
              const SizedBox(height: 24),
              
              // 颜色展示
              _buildColorShowcase(),
              const SizedBox(height: 24),
              
              // 组件演示
              _buildComponentDemo(),
              const SizedBox(height: 24),
              
              // 自定义样式演示
              _buildCustomStyleDemo(context),
              const SizedBox(height: 24),
              
              // 快速切换主题
              _buildQuickThemeSwitch(controller),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建当前主题信息
  Widget _buildCurrentThemeInfo(ThemeController controller) {
    return ShadCard(
      title: const Text('当前主题信息'),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          _buildInfoRow('主题颜色', controller.currentThemeDisplayName),
          _buildInfoRow('显示模式', controller.themeModeDisplayName),
          _buildInfoRow('主色值', AppThemeExtensions.primary.value.toRadixString(16).toUpperCase()),
          _buildInfoRow('强调色值', AppThemeExtensions.accent.value.toRadixString(16).toUpperCase()),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          Text(value),
        ],
      ),
    );
  }

  /// 构建颜色展示
  Widget _buildColorShowcase() {
    return ShadCard(
      title: const Text('颜色方案展示'),
      child: Column(
        children: [
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 2,
            children: [
              _buildColorCard('主色', AppThemeExtensions.primary),
              _buildColorCard('强调色', AppThemeExtensions.accent),
              _buildColorCard('次要色', AppThemeExtensions.secondary),
              _buildColorCard('成功色', AppThemeExtensions.success),
              _buildColorCard('警告色', AppThemeExtensions.warning),
              _buildColorCard('信息色', AppThemeExtensions.info),
              _buildColorCard('错误色', AppThemeExtensions.destructive),
              _buildColorCard('边框色', AppThemeExtensions.border),
              _buildColorCard('背景色', AppThemeExtensions.background),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建颜色卡片
  Widget _buildColorCard(String name, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppThemeExtensions.border),
      ),
      child: Center(
        child: Text(
          name,
          style: TextStyle(
            color: _getContrastColor(color),
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  /// 获取对比色（用于文字显示）
  Color _getContrastColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  /// 构建组件演示
  Widget _buildComponentDemo() {
    return ShadCard(
      title: const Text('ShadcnUI 组件演示'),
      child: Column(
        children: [
          const SizedBox(height: 16),
          
          // 按钮演示
          Row(
            children: [
              Expanded(
                child: ShadButton(
                  onPressed: () {},
                  child: const Text('主要按钮'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ShadButton.outline(
                  onPressed: () {},
                  child: const Text('次要按钮'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 输入框演示
          const ShadInput(
            placeholder: Text('请输入内容...'),
          ),
          const SizedBox(height: 16),
          
          // 开关演示
          Row(
            children: [
              const Text('开关组件'),
              const Spacer(),
              ShadSwitch(
                value: true,
                onChanged: (value) {},
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // 徽章演示
          Wrap(
            spacing: 8,
            children: [
              ShadBadge(child: const Text('Primary')),
              ShadBadge.secondary(child: const Text('Secondary')),
              ShadBadge.destructive(child: const Text('Destructive')),
              ShadBadge.outline(child: const Text('Outline')),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建自定义样式演示
  Widget _buildCustomStyleDemo(BuildContext context) {
    return ShadCard(
      title: const Text('自定义样式演示'),
      child: Column(
        children: [
          const SizedBox(height: 16),
          
          // 渐变容器
          Container(
            height: 80,
            decoration: BoxDecoration(
              gradient: context.themeGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Text(
                '主题渐变背景',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // 阴影卡片
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: context.backgroundColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: context.themeShadow(3),
              border: context.themeBorder(),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '自定义阴影卡片',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: context.foregroundColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '这个卡片使用了自定义的阴影和边框样式',
                  style: TextStyle(color: context.foregroundColor),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          
          // 状态颜色演示
          Row(
            children: [
              Expanded(
                child: _buildStatusCard('成功', context.successColor, Icons.check_circle),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatusCard('警告', context.warningColor, Icons.warning),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildStatusCard('错误', context.errorColor, Icons.error),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建状态卡片
  Widget _buildStatusCard(String label, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建快速主题切换
  Widget _buildQuickThemeSwitch(ThemeController controller) {
    return ShadCard(
      title: const Text('快速切换主题'),
      child: Column(
        children: [
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: AppThemeColor.values.length,
            itemBuilder: (context, index) {
              final themeColor = AppThemeColor.values[index];
              final isSelected = controller.currentThemeColor == themeColor;
              
              return GestureDetector(
                onTap: () => controller.setThemeColor(themeColor),
                child: Container(
                  decoration: BoxDecoration(
                    color: themeColor.seedColor,
                    borderRadius: BorderRadius.circular(8),
                    border: isSelected
                        ? Border.all(color: Colors.white, width: 3)
                        : null,
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white)
                      : null,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
