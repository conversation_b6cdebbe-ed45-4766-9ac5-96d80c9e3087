import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'theme_controller.dart';
import 'custom_color_schemes.dart';

/// 主题扩展工具类
/// 提供便捷的方式访问当前主题的颜色和样式
class AppThemeExtensions {
  /// 获取主题控制器实例
  static ThemeController get _controller => ThemeController.to;

  /// 获取当前 ShadcnUI 颜色方案
  static ShadColorScheme get currentShadColorScheme {
    return _controller.isDarkMode 
        ? _controller.shadDarkTheme.colorScheme
        : _controller.shadLightTheme.colorScheme;
  }

  /// 获取当前 Material 颜色方案
  static ColorScheme get currentMaterialColorScheme {
    return _controller.isDarkMode
        ? _controller.materialDarkTheme.colorScheme
        : _controller.materialLightTheme.colorScheme;
  }

  // === ShadcnUI 颜色快捷访问 ===
  
  /// 主色
  static Color get primary => currentShadColorScheme.primary;
  
  /// 主色前景
  static Color get primaryForeground => currentShadColorScheme.primaryForeground;
  
  /// 次要色
  static Color get secondary => currentShadColorScheme.secondary;
  
  /// 次要色前景
  static Color get secondaryForeground => currentShadColorScheme.secondaryForeground;
  
  /// 强调色
  static Color get accent => currentShadColorScheme.accent;
  
  /// 强调色前景
  static Color get accentForeground => currentShadColorScheme.accentForeground;
  
  /// 背景色
  static Color get background => currentShadColorScheme.background;
  
  /// 前景色
  static Color get foreground => currentShadColorScheme.foreground;
  
  /// 卡片背景色
  static Color get card => currentShadColorScheme.card;
  
  /// 卡片前景色
  static Color get cardForeground => currentShadColorScheme.cardForeground;
  
  /// 边框色
  static Color get border => currentShadColorScheme.border;
  
  /// 输入框边框色
  static Color get input => currentShadColorScheme.input;
  
  /// 焦点环色
  static Color get ring => currentShadColorScheme.ring;
  
  /// 破坏性操作色
  static Color get destructive => currentShadColorScheme.destructive;
  
  /// 破坏性操作前景色
  static Color get destructiveForeground => currentShadColorScheme.destructiveForeground;

  // === 扩展颜色（通过扩展方法获取） ===
  
  /// 成功色
  static Color get success => currentShadColorScheme.success;
  
  /// 警告色
  static Color get warning => currentShadColorScheme.warning;
  
  /// 信息色
  static Color get info => currentShadColorScheme.info;
  
  /// 阴影色
  static Color get shadow => currentShadColorScheme.shadow;
  
  /// 轮廓色
  static Color get outline => currentShadColorScheme.outline;

  // === 工具方法 ===
  
  /// 根据亮暗模式返回不同颜色
  /// 
  /// [lightColor] 亮色模式下的颜色
  /// [darkColor] 暗色模式下的颜色
  static Color adaptive({
    required Color lightColor,
    required Color darkColor,
  }) {
    return _controller.isDarkMode ? darkColor : lightColor;
  }

  /// 获取带透明度的主色
  /// 
  /// [opacity] 透明度 (0.0 - 1.0)
  static Color primaryWithOpacity(double opacity) {
    return primary.withValues(alpha: opacity);
  }

  /// 获取带透明度的强调色
  /// 
  /// [opacity] 透明度 (0.0 - 1.0)
  static Color accentWithOpacity(double opacity) {
    return accent.withValues(alpha: opacity);
  }

  /// 创建渐变色
  /// 
  /// [startColor] 起始颜色
  /// [endColor] 结束颜色
  /// [begin] 渐变起始位置
  /// [end] 渐变结束位置
  static LinearGradient createGradient({
    Color? startColor,
    Color? endColor,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      colors: [
        startColor ?? primary,
        endColor ?? accent,
      ],
      begin: begin,
      end: end,
    );
  }

  /// 获取主题渐变
  static LinearGradient get primaryGradient => createGradient();

  /// 获取阴影样式
  /// 
  /// [elevation] 阴影高度
  /// [color] 阴影颜色（可选）
  static List<BoxShadow> getShadow({
    double elevation = 2,
    Color? color,
  }) {
    return [
      BoxShadow(
        color: (color ?? shadow).withValues(alpha: 0.1 * elevation),
        blurRadius: 4 * elevation,
        offset: Offset(0, 2 * elevation),
      ),
    ];
  }

  /// 获取边框样式
  /// 
  /// [width] 边框宽度
  /// [color] 边框颜色（可选）
  static Border getBorder({
    double width = 1,
    Color? color,
  }) {
    return Border.all(
      color: color ?? border,
      width: width,
    );
  }

  /// 获取圆角边框样式
  /// 
  /// [radius] 圆角半径
  /// [width] 边框宽度
  /// [color] 边框颜色（可选）
  static BoxDecoration getRoundedBorder({
    double radius = 8,
    double width = 1,
    Color? color,
  }) {
    return BoxDecoration(
      border: getBorder(width: width, color: color),
      borderRadius: BorderRadius.circular(radius),
    );
  }
}

/// BuildContext 扩展
/// 为 BuildContext 添加便捷的主题访问方法
extension ThemeContextExtension on BuildContext {
  /// 获取 ShadcnUI 颜色方案
  ShadColorScheme get shadColors => AppThemeExtensions.currentShadColorScheme;
  
  /// 获取 Material 颜色方案
  ColorScheme get materialColors => AppThemeExtensions.currentMaterialColorScheme;
  
  // === 快捷颜色访问 ===
  
  /// 主色
  Color get primaryColor => AppThemeExtensions.primary;
  
  /// 强调色
  Color get accentColor => AppThemeExtensions.accent;
  
  /// 背景色
  Color get backgroundColor => AppThemeExtensions.background;
  
  /// 前景色
  Color get foregroundColor => AppThemeExtensions.foreground;
  
  /// 成功色
  Color get successColor => AppThemeExtensions.success;
  
  /// 警告色
  Color get warningColor => AppThemeExtensions.warning;
  
  /// 信息色
  Color get infoColor => AppThemeExtensions.info;
  
  /// 错误色
  Color get errorColor => AppThemeExtensions.destructive;
  
  // === 工具方法 ===
  
  /// 获取主题渐变
  LinearGradient get themeGradient => AppThemeExtensions.primaryGradient;
  
  /// 获取阴影
  List<BoxShadow> themeShadow([double elevation = 2]) => 
      AppThemeExtensions.getShadow(elevation: elevation);
  
  /// 获取边框
  Border themeBorder([double width = 1]) => 
      AppThemeExtensions.getBorder(width: width);
}
