import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'custom_color_schemes.dart';

/// 主题颜色枚举
/// 定义应用支持的所有主题颜色
enum AppThemeColor {
  /// 蓝色主题
  blue('蓝色', Colors.blue),
  
  /// 绿色主题
  green('绿色', Colors.green),
  
  /// 紫色主题
  purple('紫色', Colors.purple),
  
  /// 橙色主题
  orange('橙色', Colors.orange),
  
  /// 红色主题
  red('红色', Colors.red),
  
  /// 青色主题
  teal('青色', Colors.teal),
  
  /// 粉色主题
  pink('粉色', Colors.pink),
  
  /// 靛蓝主题
  indigo('靛蓝', Colors.indigo);

  const AppThemeColor(this.displayName, this.seedColor);
  
  /// 主题显示名称
  final String displayName;
  
  /// 主题种子颜色
  final Color seedColor;
}

/// ShadcnUI 颜色方案映射
/// 将应用主题颜色映射到 ShadcnUI 的颜色方案
class ShadColorSchemeMapper {
  /// 获取亮色模式的 ShadcnUI 颜色方案
  static ShadColorScheme getLightColorScheme(AppThemeColor themeColor) {
    switch (themeColor) {
      case AppThemeColor.blue:
        return const ShadBlueColorScheme.light();
      case AppThemeColor.green:
        return const ShadGreenColorScheme.light();
      case AppThemeColor.purple:
        return const ShadVioletColorScheme.light();
      case AppThemeColor.orange:
        return const ShadOrangeColorScheme.light();
      case AppThemeColor.red:
        return const ShadRedColorScheme.light();
      case AppThemeColor.teal:
        return const ShadTealColorScheme.light(); // 使用自定义青色方案
      case AppThemeColor.pink:
        return const ShadRoseColorScheme.light();
      case AppThemeColor.indigo:
        return const ShadIndigoColorScheme.light(); // 使用自定义靛蓝方案
    }
  }

  /// 获取暗色模式的 ShadcnUI 颜色方案
  static ShadColorScheme getDarkColorScheme(AppThemeColor themeColor) {
    switch (themeColor) {
      case AppThemeColor.blue:
        return const ShadBlueColorScheme.dark();
      case AppThemeColor.green:
        return const ShadGreenColorScheme.dark();
      case AppThemeColor.purple:
        return const ShadVioletColorScheme.dark();
      case AppThemeColor.orange:
        return const ShadOrangeColorScheme.dark();
      case AppThemeColor.red:
        return const ShadRedColorScheme.dark();
      case AppThemeColor.teal:
        return const ShadTealColorScheme.dark(); // 使用自定义青色方案
      case AppThemeColor.pink:
        return const ShadRoseColorScheme.dark();
      case AppThemeColor.indigo:
        return const ShadIndigoColorScheme.dark(); // 使用自定义靛蓝方案
    }
  }
}
