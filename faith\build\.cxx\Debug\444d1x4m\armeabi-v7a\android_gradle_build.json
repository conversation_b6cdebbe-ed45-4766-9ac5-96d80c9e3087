{"buildFiles": ["D:\\environment\\Flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\environment\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\code\\cool-uniapp\\faith\\build\\.cxx\\Debug\\444d1x4m\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\environment\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\code\\cool-uniapp\\faith\\build\\.cxx\\Debug\\444d1x4m\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\environment\\Android\\SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\environment\\Android\\SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}