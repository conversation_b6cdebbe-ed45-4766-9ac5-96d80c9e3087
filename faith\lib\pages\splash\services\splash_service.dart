import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/splash_config.dart';

/// Splash 配置服务
/// 负责从后台获取启动页面的配置信息
class SplashService extends GetxController {
  static SplashService get to => Get.find();

  /// 当前配置
  final _config = SplashConfig.defaultConfig.obs;
  SplashConfig get config => _config.value;

  /// 是否正在加载配置
  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    // 应用启动时加载配置
    loadConfig();
  }

  /// 从后台加载配置
  Future<void> loadConfig() async {
    try {
      _isLoading.value = true;
      
      // 模拟从后台 API 获取配置
      // 实际项目中这里应该是真实的 API 调用
      final configData = await _fetchConfigFromApi();
      
      if (configData != null) {
        _config.value = SplashConfig.fromJson(configData);
      }
    } catch (e) {
      // 加载失败时使用默认配置
      debugPrint('加载 Splash 配置失败: $e');
      _config.value = SplashConfig.defaultConfig;
    } finally {
      _isLoading.value = false;
    }
  }

  /// 模拟从 API 获取配置数据
  /// 实际项目中替换为真实的 HTTP 请求
  Future<Map<String, dynamic>?> _fetchConfigFromApi() async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));
    
    // 可以在这里切换不同的预设配置
    // 0: 默认配置, 1: 自然风景, 2: 科技主题, 3: 简约主题
    final presetIndex = 3; // 改为 2 使用科技主题

    final presets = getPresetConfigs();
    if (presetIndex < presets.length) {
      return presets[presetIndex].toJson();
    }

    // 默认配置
    return SplashConfig.defaultConfig.toJson();
  }

  /// 更新配置（用于测试或动态更新）
  void updateConfig(SplashConfig newConfig) {
    _config.value = newConfig;
  }

  /// 设置背景图片 URL
  void setBackgroundImageUrl(String url) {
    _config.value = _config.value.copyWith(backgroundImageUrl: url);
  }

  /// 设置应用名称
  void setAppName(String name) {
    _config.value = _config.value.copyWith(appName: name);
  }

  /// 设置副标题
  void setSubtitle(String subtitle) {
    _config.value = _config.value.copyWith(subtitle: subtitle);
  }

  /// 切换到预设配置
  void switchToPresetConfig(int index) {
    final presets = getPresetConfigs();
    if (index >= 0 && index < presets.length) {
      _config.value = presets[index];
    }
  }

  /// 获取当前配置在预设中的索引
  int getCurrentPresetIndex() {
    final presets = getPresetConfigs();
    for (int i = 0; i < presets.length; i++) {
      if (_isConfigEqual(_config.value, presets[i])) {
        return i;
      }
    }
    return -1; // 不是预设配置
  }

  /// 比较两个配置是否相等
  bool _isConfigEqual(SplashConfig config1, SplashConfig config2) {
    return config1.appName == config2.appName &&
           config1.subtitle == config2.subtitle &&
           config1.backgroundImageUrl == config2.backgroundImageUrl &&
           config1.backgroundImageAsset == config2.backgroundImageAsset;
  }

  /// 获取一些预设的配置示例
  static List<SplashConfig> getPresetConfigs() {
    return [
      // 默认配置
      SplashConfig.defaultConfig,
      
      // 自然风景主题
      const SplashConfig(
        appName: 'Faith',
        subtitle: '探索 · 发现 · 成长',
        backgroundImageUrl: 'https://haowallpaper.com/link/common/file/getCroppingImg/17146456186080640',
        displayDuration: 5,
      ),
      
      // 科技主题
      const SplashConfig(
        appName: 'Faith',
        subtitle: '创新 · 科技 · 未来',
        backgroundImageUrl: 'https://haowallpaper.com/link/common/file/getCroppingImg/17146456186080640',
        displayDuration: 5,
      ),
      
      // 简约主题
      const SplashConfig(
        appName: 'Faith',
        subtitle: '简约 · 纯净 · 专注',
        displayDuration: 3,
      ),
    ];
  }
}

/// Splash 配置扩展方法
extension SplashConfigExtension on SplashConfig {
  /// 是否有背景图片
  bool get hasBackgroundImage => 
      (backgroundImageUrl != null && backgroundImageUrl!.isNotEmpty) ||
      (backgroundImageAsset != null && backgroundImageAsset!.isNotEmpty);
  
  /// 获取背景图片类型
  String get backgroundImageType {
    if (backgroundImageUrl != null && backgroundImageUrl!.isNotEmpty) {
      return 'network';
    } else if (backgroundImageAsset != null && backgroundImageAsset!.isNotEmpty) {
      return 'asset';
    } else {
      return 'gradient';
    }
  }
}
