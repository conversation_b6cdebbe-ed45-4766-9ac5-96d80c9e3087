import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:faith/theme/theme.dart';

class ThemeController extends GetxController {
  static ThemeController get to => Get.find();

  final _box = GetStorage();
  final _darkModeKey = 'isDarkMode';
  final _themeColorKey = 'themeColor';

  // 响应式变量
  final _isDarkMode = false.obs;
  final _currentThemeColor = ThemeColor.blue.obs;

  bool get isDarkMode => _isDarkMode.value;
  ThemeColor get currentThemeColor => _currentThemeColor.value;

  @override
  void onInit() {
    super.onInit();
    // 从本地存储读取主题设置
    _isDarkMode.value = _box.read(_darkModeKey) ?? false;

    // 读取颜色主题设置
    final colorIndex = _box.read(_themeColorKey) ?? 0;
    _currentThemeColor.value = ThemeColor.values[colorIndex];

    // 应用初始主题
    _applyTheme();
  }

  /// 切换亮色/暗色主题
  void toggleTheme() {
    _isDarkMode.value = !_isDarkMode.value;
    _box.write(_darkModeKey, _isDarkMode.value);
    _applyTheme();
  }

  /// 设置亮色/暗色主题
  void setTheme(bool isDark) {
    _isDarkMode.value = isDark;
    _box.write(_darkModeKey, isDark);
    _applyTheme();
  }

  /// 设置颜色主题
  void setThemeColor(ThemeColor themeColor) {
    _currentThemeColor.value = themeColor;
    _box.write(_themeColorKey, themeColor.index);
    _applyTheme();
  }

  /// 应用主题到应用
  void _applyTheme() {
    final lightTheme = AppTheme.getLightTheme(_currentThemeColor.value);
    final darkTheme = AppTheme.getDarkTheme(_currentThemeColor.value);

    Get.changeTheme(lightTheme);
    Get.changeThemeMode(_isDarkMode.value ? ThemeMode.dark : ThemeMode.light);

    if (_isDarkMode.value) {
      Get.changeTheme(darkTheme);
    }

    // 强制更新 UI
    update();
  }

  /// 获取当前主题模式
  ThemeMode get themeMode => _isDarkMode.value ? ThemeMode.dark : ThemeMode.light;

  /// 获取当前亮色主题
  ThemeData get lightTheme => AppTheme.getLightTheme(_currentThemeColor.value);

  /// 获取当前暗色主题
  ThemeData get darkTheme => AppTheme.getDarkTheme(_currentThemeColor.value);

  /// 获取当前 ShadcnUI 亮色主题
  get shadLightTheme => AppTheme.getShadLightTheme(_currentThemeColor.value);

  /// 获取当前 ShadcnUI 暗色主题
  get shadDarkTheme => AppTheme.getShadDarkTheme(_currentThemeColor.value);
}
