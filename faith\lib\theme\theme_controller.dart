import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'colors.dart';
import 'theme_config.dart';

/// 主题控制器
/// 负责管理应用的主题状态，包括亮暗模式和主题颜色
class ThemeController extends GetxController {
  /// 获取主题控制器实例
  static ThemeController get to => Get.find();

  // 本地存储
  final _storage = GetStorage();
  
  // 存储键
  static const String _isDarkModeKey = 'is_dark_mode';
  static const String _themeColorKey = 'theme_color';

  // 响应式状态
  final _isDarkMode = false.obs;
  final _currentThemeColor = AppThemeColor.blue.obs;

  /// 是否为暗色模式
  bool get isDarkMode => _isDarkMode.value;

  /// 当前主题颜色
  AppThemeColor get currentThemeColor => _currentThemeColor.value;

  /// 当前主题模式
  ThemeMode get themeMode => _isDarkMode.value ? ThemeMode.dark : ThemeMode.light;

  /// 当前 Material 亮色主题
  ThemeData get materialLightTheme => AppThemeConfig.getMaterialLightTheme(_currentThemeColor.value);

  /// 当前 Material 暗色主题
  ThemeData get materialDarkTheme => AppThemeConfig.getMaterialDarkTheme(_currentThemeColor.value);

  /// 当前 ShadcnUI 亮色主题
  get shadLightTheme => AppThemeConfig.getShadLightTheme(_currentThemeColor.value);

  /// 当前 ShadcnUI 暗色主题
  get shadDarkTheme => AppThemeConfig.getShadDarkTheme(_currentThemeColor.value);

  @override
  void onInit() {
    super.onInit();
    _loadThemeSettings();
  }

  /// 从本地存储加载主题设置
  void _loadThemeSettings() {
    // 加载亮暗模式设置
    _isDarkMode.value = _storage.read(_isDarkModeKey) ?? false;
    
    // 加载主题颜色设置
    final colorIndex = _storage.read(_themeColorKey) ?? 0;
    if (colorIndex >= 0 && colorIndex < AppThemeColor.values.length) {
      _currentThemeColor.value = AppThemeColor.values[colorIndex];
    }
  }

  /// 切换亮暗模式
  void toggleThemeMode() {
    _isDarkMode.value = !_isDarkMode.value;
    _saveThemeSettings();
    _applyTheme();
  }

  /// 设置主题模式
  /// 
  /// [isDark] 是否为暗色模式
  void setThemeMode(bool isDark) {
    if (_isDarkMode.value != isDark) {
      _isDarkMode.value = isDark;
      _saveThemeSettings();
      _applyTheme();
    }
  }

  /// 设置主题颜色
  /// 
  /// [themeColor] 新的主题颜色
  void setThemeColor(AppThemeColor themeColor) {
    if (_currentThemeColor.value != themeColor) {
      _currentThemeColor.value = themeColor;
      _saveThemeSettings();
      _applyTheme();
    }
  }

  /// 保存主题设置到本地存储
  void _saveThemeSettings() {
    _storage.write(_isDarkModeKey, _isDarkMode.value);
    _storage.write(_themeColorKey, _currentThemeColor.value.index);
  }

  /// 应用主题到应用
  void _applyTheme() {
    // 更新 GetX 的主题
    Get.changeTheme(materialLightTheme);
    Get.changeThemeMode(themeMode);
    
    // 如果是暗色模式，应用暗色主题
    if (_isDarkMode.value) {
      Get.changeTheme(materialDarkTheme);
    }
    
    // 通知 UI 更新
    update();
  }

  /// 重置主题为默认设置
  void resetTheme() {
    _isDarkMode.value = false;
    _currentThemeColor.value = AppThemeColor.blue;
    _saveThemeSettings();
    _applyTheme();
  }

  /// 获取主题颜色的显示名称
  String get currentThemeDisplayName => _currentThemeColor.value.displayName;

  /// 获取主题模式的显示名称
  String get themeModeDisplayName => _isDarkMode.value ? '暗色模式' : '亮色模式';
}
