# Faith 主题系统

## 概述

Faith 应用采用了简洁优雅的主题系统设计，支持：

- ✅ 亮色/暗色模式切换
- ✅ 8种预定义主题颜色
- ✅ ShadcnUI 组件完美集成
- ✅ 自动持久化存储
- ✅ 响应式主题更新

## 架构设计

```
theme/
├── colors.dart              # 主题颜色定义
├── theme_config.dart        # 主题配置生成
├── theme_controller.dart    # 主题状态管理
├── theme_settings_page.dart # 主题设置页面
└── README.md               # 使用文档
```

## 核心组件

### 1. AppThemeColor (colors.dart)
定义应用支持的主题颜色枚举：
- blue (蓝色)
- green (绿色) 
- purple (紫色)
- orange (橙色)
- red (红色)
- teal (青色)
- pink (粉色)
- indigo (靛蓝)

### 2. AppThemeConfig (theme_config.dart)
负责生成 Material 和 ShadcnUI 主题配置：
- `getMaterialLightTheme()` - Material 亮色主题
- `getMaterialDarkTheme()` - Material 暗色主题
- `getShadLightTheme()` - ShadcnUI 亮色主题
- `getShadDarkTheme()` - ShadcnUI 暗色主题

### 3. ThemeController (theme_controller.dart)
主题状态管理控制器：
- 响应式状态管理
- 本地存储持久化
- 主题切换逻辑

## 使用方法

### 基本用法

```dart
// 获取主题控制器
final themeController = ThemeController.to;

// 切换亮暗模式
themeController.toggleThemeMode();

// 设置特定模式
themeController.setThemeMode(true);  // 暗色模式
themeController.setThemeMode(false); // 亮色模式

// 设置主题颜色
themeController.setThemeColor(AppThemeColor.blue);

// 重置主题
themeController.resetTheme();
```

### 在 Widget 中使用

```dart
// 响应主题变化
GetBuilder<ThemeController>(
  builder: (controller) => Container(
    color: Theme.of(context).colorScheme.primary,
    child: Text(
      '当前主题: ${controller.currentThemeDisplayName}',
      style: TextStyle(
        color: Theme.of(context).colorScheme.onPrimary,
      ),
    ),
  ),
)

// 使用 ShadcnUI 组件
ShadButton(
  onPressed: () => ThemeController.to.toggleThemeMode(),
  child: Text('切换主题'),
)
```

### 获取主题信息

```dart
final controller = ThemeController.to;

// 当前状态
bool isDark = controller.isDarkMode;
AppThemeColor currentColor = controller.currentThemeColor;
ThemeMode mode = controller.themeMode;

// 显示名称
String colorName = controller.currentThemeDisplayName;
String modeName = controller.themeModeDisplayName;

// 主题数据
ThemeData lightTheme = controller.materialLightTheme;
ThemeData darkTheme = controller.materialDarkTheme;
```

## 最佳实践

### 1. 初始化
在 `main.dart` 中正确初始化：

```dart
Future<void> _initializeApp() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  Get.put(ThemeController()); // 注册主题控制器
}
```

### 2. 应用主题
在应用根部使用主题：

```dart
GetBuilder<ThemeController>(
  builder: (themeController) => ShadApp(
    theme: themeController.shadLightTheme,
    darkTheme: themeController.shadDarkTheme,
    themeMode: themeController.themeMode,
    home: GetMaterialApp(
      theme: themeController.materialLightTheme,
      darkTheme: themeController.materialDarkTheme,
      themeMode: themeController.themeMode,
      // ...
    ),
  ),
)
```

### 3. 响应式更新
使用 `GetBuilder` 确保 UI 响应主题变化：

```dart
GetBuilder<ThemeController>(
  builder: (controller) => YourWidget(),
)
```

### 4. 主题设置页面
直接使用提供的主题设置页面：

```dart
// 路由配置
GetPage(
  name: '/theme-settings',
  page: () => const ThemeSettingsPage(),
)

// 导航到设置页面
Get.toNamed('/theme-settings');
```

## 扩展指南

### 添加新的主题颜色

1. 在 `AppThemeColor` 枚举中添加新颜色
2. 在 `ShadColorSchemeMapper` 中添加对应映射
3. 主题会自动生效

### 自定义主题配置

修改 `AppThemeConfig` 中的主题生成方法，可以自定义：
- 组件样式
- 颜色方案
- 字体配置
- 动画效果

## 注意事项

1. 确保在使用主题控制器前已完成初始化
2. 使用 `GetBuilder` 包装需要响应主题变化的 Widget
3. 主题设置会自动保存到本地存储
4. ShadcnUI 组件会自动应用主题样式
5. 某些颜色（如青色、靛蓝）使用相近的 ShadcnUI 颜色方案作为替代

## 技术特性

- **响应式**: 基于 GetX 的响应式状态管理
- **持久化**: 使用 GetStorage 自动保存设置
- **类型安全**: 完整的类型定义和枚举
- **性能优化**: 最小化重建范围
- **易扩展**: 清晰的架构便于扩展
- **最佳实践**: 遵循 Flutter 和 GetX 最佳实践
