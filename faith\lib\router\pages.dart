import 'package:faith/pages/splash/views/splash_page_with_bg.dart';
import 'package:flutter/material.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:faith/pages/error/error_404.dart';
import 'package:faith/pages/home/<USER>';
import 'package:faith/pages/splash/views/splash_page.dart';
import 'package:faith/theme/theme_settings_page.dart';
import 'package:faith/theme/theme_demo_page.dart';

class Routes {
  static final List<GetPage<dynamic>> getPages = [
    CustomGetPage(
      name: '/splash',
      page: () => const SplashPage(),
      // 如果需要背景图版本，可以使用：
      // page: () => const SplashPageWithBg(),
      fullscreen: true, // 设置为全屏
    ),
    CustomGetPage(
      name: '/home',
      page: () => const HomeIndex(),
      fullscreen: true, // 设置为全屏
    ),
    CustomGetPage(
      name: '/theme-settings',
      page: () => const ThemeSettingsPage(),
    ),
    CustomGetPage(
      name: '/theme-demo',
      page: () => const ThemeDemoPage(),
    ),
  ];

  static final unknownRoute = CustomGetPage(
    name: '/error_404',
    page: () => const Error404Screen(),
  );
}

class CustomGetPage extends GetPage<dynamic> {
  final bool? fullscreen;

  CustomGetPage({
    required super.name,
    required super.page,
    super.binding,
    this.fullscreen,
    super.transitionDuration,
    super.customTransition,
    super.preventDuplicates,
    super.transition = Transition.native,
  }) : super(
         curve: Curves.linear,
         showCupertinoParallax: false,
         popGesture: false,
         fullscreenDialog: fullscreen != null && fullscreen,
       );
}
