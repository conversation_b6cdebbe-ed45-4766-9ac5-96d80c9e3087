import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

/// 自定义 Teal (青色) ShadColorScheme
class ShadTealColorScheme extends ShadColorScheme {
  const ShadTealColorScheme.light()
      : super(
          background: const Color(0xFFFFFFFF),
          foreground: const Color(0xFF0F172A),
          card: const Color(0xFFFFFFFF),
          cardForeground: const Color(0xFF0F172A),
          popover: const Color(0xFFFFFFFF),
          popoverForeground: const Color(0xFF0F172A),
          primary: const Color(0xFF0D9488),
          primaryForeground: const Color(0xFFF0FDFA),
          secondary: const Color(0xFFF1F5F9),
          secondaryForeground: const Color(0xFF0F172A),
          muted: const Color(0xFFF1F5F9),
          mutedForeground: const Color(0xFF64748B),
          accent: const Color(0xFFF1F5F9),
          accentForeground: const Color(0xFF0F172A),
          destructive: const Color(0xFFEF4444),
          destructiveForeground: const Color(0xFFFEF2F2),
          border: const Color(0xFFE2E8F0),
          input: const Color(0xFFE2E8F0),
          ring: const Color(0xFF0D9488),
          selection: const Color(0xFFB3F5EC),
        );

  const ShadTealColorScheme.dark()
      : super(
          background: const Color(0xFF0F172A),
          foreground: const Color(0xFFF8FAFC),
          card: const Color(0xFF0F172A),
          cardForeground: const Color(0xFFF8FAFC),
          popover: const Color(0xFF0F172A),
          popoverForeground: const Color(0xFFF8FAFC),
          primary: const Color(0xFF14B8A6),
          primaryForeground: const Color(0xFF042F2E),
          secondary: const Color(0xFF1E293B),
          secondaryForeground: const Color(0xFFF8FAFC),
          muted: const Color(0xFF1E293B),
          mutedForeground: const Color(0xFF94A3B8),
          accent: const Color(0xFF1E293B),
          accentForeground: const Color(0xFFF8FAFC),
          destructive: const Color(0xFF7F1D1D),
          destructiveForeground: const Color(0xFFF8FAFC),
          border: const Color(0xFF1E293B),
          input: const Color(0xFF1E293B),
          ring: const Color(0xFF14B8A6),
          selection: const Color(0xFF134E4A),
        );
}

/// 自定义 Indigo (靛蓝) ShadColorScheme
class ShadIndigoColorScheme extends ShadColorScheme {
  const ShadIndigoColorScheme.light()
      : super(
          background: const Color(0xFFFFFFFF),
          foreground: const Color(0xFF1E1B4B),
          card: const Color(0xFFFFFFFF),
          cardForeground: const Color(0xFF1E1B4B),
          popover: const Color(0xFFFFFFFF),
          popoverForeground: const Color(0xFF1E1B4B),
          primary: const Color(0xFF4F46E5),
          primaryForeground: const Color(0xFFF0F0FF),
          secondary: const Color(0xFFF1F5F9),
          secondaryForeground: const Color(0xFF1E1B4B),
          muted: const Color(0xFFF1F5F9),
          mutedForeground: const Color(0xFF64748B),
          accent: const Color(0xFFF1F5F9),
          accentForeground: const Color(0xFF1E1B4B),
          destructive: const Color(0xFFEF4444),
          destructiveForeground: const Color(0xFFFEF2F2),
          border: const Color(0xFFE2E8F0),
          input: const Color(0xFFE2E8F0),
          ring: const Color(0xFF4F46E5),
          selection: const Color(0xFFE0E7FF),
        );

  const ShadIndigoColorScheme.dark()
      : super(
          background: const Color(0xFF1E1B4B),
          foreground: const Color(0xFFF8FAFC),
          card: const Color(0xFF1E1B4B),
          cardForeground: const Color(0xFFF8FAFC),
          popover: const Color(0xFF1E1B4B),
          popoverForeground: const Color(0xFFF8FAFC),
          primary: const Color(0xFF6366F1),
          primaryForeground: const Color(0xFF312E81),
          secondary: const Color(0xFF312E81),
          secondaryForeground: const Color(0xFFF8FAFC),
          muted: const Color(0xFF312E81),
          mutedForeground: const Color(0xFF94A3B8),
          accent: const Color(0xFF312E81),
          accentForeground: const Color(0xFFF8FAFC),
          destructive: const Color(0xFF7F1D1D),
          destructiveForeground: const Color(0xFFF8FAFC),
          border: const Color(0xFF312E81),
          input: const Color(0xFF312E81),
          ring: const Color(0xFF6366F1),
          selection: const Color(0xFF4338CA),
        );
}

/// 扩展现有的 ShadColorScheme，添加更多颜色选项
extension ShadColorSchemeExtension on ShadColorScheme {
  /// 获取成功色
  Color get success => const Color(0xFF10B981);
  
  /// 获取警告色
  Color get warning => const Color(0xFFF59E0B);
  
  /// 获取信息色
  Color get info => const Color(0xFF3B82F6);
  
  /// 获取表面变体色
  Color get surfaceVariant => muted;
  
  /// 获取轮廓色
  Color get outline => border;
  
  /// 获取轮廓变体色
  Color get outlineVariant => border.withValues(alpha: 0.5);
  
  /// 获取阴影色
  Color get shadow => foreground.withValues(alpha: 0.1);
  
  /// 获取表面色调色
  Color get surfaceTint => primary;
}
