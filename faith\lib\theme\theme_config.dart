import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'colors.dart';

/// 应用主题配置类
/// 负责生成 Material 和 ShadcnUI 主题
class AppThemeConfig {
  /// 生成 Material 亮色主题
  /// 
  /// [themeColor] 主题颜色
  /// 返回配置好的 Material 亮色主题
  static ThemeData getMaterialLightTheme(AppThemeColor themeColor) {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: themeColor.seedColor,
      brightness: Brightness.light,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: colorScheme,
      
      // AppBar 主题
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
        surfaceTintColor: Colors.transparent,
      ),
      
      // 卡片主题
      cardTheme: CardThemeData(
        color: colorScheme.surface,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        surfaceTintColor: Colors.transparent,
      ),
      
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        filled: true,
        fillColor: colorScheme.surface,
      ),
    );
  }

  /// 生成 Material 暗色主题
  /// 
  /// [themeColor] 主题颜色
  /// 返回配置好的 Material 暗色主题
  static ThemeData getMaterialDarkTheme(AppThemeColor themeColor) {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: themeColor.seedColor,
      brightness: Brightness.dark,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: colorScheme,
      
      // AppBar 主题
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
        surfaceTintColor: Colors.transparent,
      ),
      
      // 卡片主题
      cardTheme: CardThemeData(
        color: colorScheme.surface,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        surfaceTintColor: Colors.transparent,
      ),
      
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        filled: true,
        fillColor: colorScheme.surface,
      ),
    );
  }

  /// 生成 ShadcnUI 亮色主题
  /// 
  /// [themeColor] 主题颜色
  /// 返回配置好的 ShadcnUI 亮色主题
  static ShadThemeData getShadLightTheme(AppThemeColor themeColor) {
    return ShadThemeData(
      brightness: Brightness.light,
      colorScheme: ShadColorSchemeMapper.getLightColorScheme(themeColor),
    );
  }

  /// 生成 ShadcnUI 暗色主题
  /// 
  /// [themeColor] 主题颜色
  /// 返回配置好的 ShadcnUI 暗色主题
  static ShadThemeData getShadDarkTheme(AppThemeColor themeColor) {
    return ShadThemeData(
      brightness: Brightness.dark,
      colorScheme: ShadColorSchemeMapper.getDarkColorScheme(themeColor),
    );
  }
}
