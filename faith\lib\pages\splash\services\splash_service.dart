import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/splash_config.dart';

/// Splash 配置服务
/// 负责从后台获取启动页面的配置信息
class SplashService extends GetxController {
  static SplashService get to => Get.find();

  /// 当前配置
  final _config = SplashConfig.defaultConfig.obs;
  SplashConfig get config => _config.value;

  /// 是否正在加载配置
  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    // 应用启动时加载配置
    loadConfig();
  }

  /// 从后台加载配置
  Future<void> loadConfig() async {
    try {
      _isLoading.value = true;
      
      // 模拟从后台 API 获取配置
      // 实际项目中这里应该是真实的 API 调用
      final configData = await _fetchConfigFromApi();
      
      if (configData != null) {
        _config.value = SplashConfig.fromJson(configData);
      }
    } catch (e) {
      // 加载失败时使用默认配置
      debugPrint('加载 Splash 配置失败: $e');
      _config.value = SplashConfig.defaultConfig;
    } finally {
      _isLoading.value = false;
    }
  }

  /// 模拟从 API 获取配置数据
  /// 实际项目中替换为真实的 HTTP 请求
  Future<Map<String, dynamic>?> _fetchConfigFromApi() async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    // 模拟后台 API 返回的数据
    // 如果后台没有配置或接口失败，返回 null 使用默认配置
    // 如果后台有配置，返回具体的配置数据

    // 示例1：后台没有配置，返回 null（将使用默认配置）
    // return null;

    // 示例2：后台有配置，返回具体配置（取消注释下面的代码来测试）
    return {
      'app_name': 'Faith Pro',
      'subtitle': '后台配置 · 动态更新',
      'background_image_url': 'https://haowallpaper.com/link/common/file/getCroppingImg/17146456186080640',
      'display_duration': 5,
      'show_skip_button': true,
      'version': '1.0.0',
      'copyright': '© 2024 Faith Pro App',
    };
  }

  /// 更新配置（用于测试或动态更新）
  void updateConfig(SplashConfig newConfig) {
    _config.value = newConfig;
  }

  /// 设置背景图片 URL
  void setBackgroundImageUrl(String url) {
    _config.value = _config.value.copyWith(backgroundImageUrl: url);
  }

  /// 设置应用名称
  void setAppName(String name) {
    _config.value = _config.value.copyWith(appName: name);
  }

  /// 设置副标题
  void setSubtitle(String subtitle) {
    _config.value = _config.value.copyWith(subtitle: subtitle);
  }

  /// 测试方法：模拟后台是否有配置数据
  /// [hasBackendData] true: 模拟后台有配置数据，false: 模拟后台无数据
  void setTestConfig(bool hasBackendData) {
    if (hasBackendData) {
      // 模拟后台返回的配置数据
      _config.value = const SplashConfig(
        appName: 'Faith Pro',
        subtitle: '后台配置 · 动态更新',
        backgroundImageUrl: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800',
        displayDuration: 5,
        showSkipButton: true,
        version: '1.0.0',
        copyright: '© 2024 Faith Pro App',
      );
    } else {
      // 使用默认配置
      _config.value = SplashConfig.defaultConfig;
    }
    update(); // 通知 UI 更新
  }
}

/// Splash 配置扩展方法
extension SplashConfigExtension on SplashConfig {
  /// 是否有背景图片
  bool get hasBackgroundImage => 
      (backgroundImageUrl != null && backgroundImageUrl!.isNotEmpty) ||
      (backgroundImageAsset != null && backgroundImageAsset!.isNotEmpty);
  
  /// 获取背景图片类型
  String get backgroundImageType {
    if (backgroundImageUrl != null && backgroundImageUrl!.isNotEmpty) {
      return 'network';
    } else if (backgroundImageAsset != null && backgroundImageAsset!.isNotEmpty) {
      return 'asset';
    } else {
      return 'gradient';
    }
  }
}
