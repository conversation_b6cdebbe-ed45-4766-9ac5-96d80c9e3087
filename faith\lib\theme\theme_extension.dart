import 'package:flutter/material.dart';
import 'package:faith/controllers/theme_controller.dart';
import 'package:faith/theme/theme.dart';

/// 主题扩展类，提供便捷的颜色访问方法
class AppThemeExtension {
  static ThemeController get _controller => ThemeController.to;
  
  /// 获取当前主题的自定义颜色
  static Color getCustomColor(String key) {
    return _controller.currentThemeColor.getCustomColor(key);
  }
  
  /// 获取当前主题的强调色
  static Color get accent => getCustomColor('accent');
  
  /// 获取当前主题的浅色
  static Color get light => getCustomColor('light');
  
  /// 获取当前主题的深色
  static Color get dark => getCustomColor('dark');
  
  /// 获取当前主题的渐变色1
  static Color get gradient1 => getCustomColor('gradient1');
  
  /// 获取当前主题的渐变色2
  static Color get gradient2 => getCustomColor('gradient2');
  
  /// 获取当前主题的渐变
  static LinearGradient get gradient => LinearGradient(
    colors: [gradient1, gradient2],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  /// 获取当前主题的主色
  static Color get primary => _controller.currentThemeColor.color;
  
  /// 根据亮暗模式返回不同颜色
  static Color adaptive({required Color light, required Color dark}) {
    return _controller.isDarkMode ? dark : light;
  }
  
  /// 获取带透明度的自定义颜色
  static Color getCustomColorWithOpacity(String key, double opacity) {
    return getCustomColor(key).withValues(alpha: opacity);
  }
  
  /// 创建自定义渐变
  static LinearGradient createGradient({
    required String startColorKey,
    required String endColorKey,
    Alignment begin = Alignment.topLeft,
    Alignment end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      colors: [getCustomColor(startColorKey), getCustomColor(endColorKey)],
      begin: begin,
      end: end,
    );
  }
  
  /// 获取阴影颜色
  static Color getShadowColor({double opacity = 0.1}) {
    return primary.withValues(alpha: opacity);
  }
  
  /// 获取边框颜色
  static Color getBorderColor({double opacity = 0.2}) {
    return primary.withValues(alpha: opacity);
  }
}

/// BuildContext 扩展，方便在 Widget 中使用
extension ThemeContextExtension on BuildContext {
  /// 获取自定义主题颜色
  Color customColor(String key) => AppThemeExtension.getCustomColor(key);
  
  /// 获取主题强调色
  Color get accentColor => AppThemeExtension.accent;
  
  /// 获取主题浅色
  Color get lightColor => AppThemeExtension.light;
  
  /// 获取主题深色
  Color get darkColor => AppThemeExtension.dark;
  
  /// 获取主题渐变
  LinearGradient get themeGradient => AppThemeExtension.gradient;
  
  /// 获取主题主色
  Color get primaryColor => AppThemeExtension.primary;
  
  /// 获取阴影颜色
  Color shadowColor({double opacity = 0.1}) => AppThemeExtension.getShadowColor(opacity: opacity);
  
  /// 获取边框颜色
  Color borderColor({double opacity = 0.2}) => AppThemeExtension.getBorderColor(opacity: opacity);
}
