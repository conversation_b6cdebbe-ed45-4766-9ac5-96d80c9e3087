import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';

/// 主题颜色枚举
enum ThemeColor {
  blue('蓝色', Colors.blue, {
    'accent': Color(0xFF1E40AF),
    'light': Color(0xFFDBEAFE),
    'dark': Color(0xFF1E3A8A),
    'gradient1': Color(0xFF3B82F6),
    'gradient2': Color(0xFF1D4ED8),
  }),
  green('绿色', Colors.green, {
    'accent': Color(0xFF059669),
    'light': Color(0xFFD1FAE5),
    'dark': Color(0xFF064E3B),
    'gradient1': Color(0xFF10B981),
    'gradient2': Color(0xFF047857),
  }),
  purple('紫色', Colors.purple, {
    'accent': Color(0xFF7C3AED),
    'light': Color(0xFFEDE9FE),
    'dark': Color(0xFF581C87),
    'gradient1': Color(0xFF8B5CF6),
    'gradient2': Color(0xFF6D28D9),
  }),
  orange('橙色', Colors.orange, {
    'accent': Color(0xFFEA580C),
    'light': Color(0xFFFED7AA),
    'dark': Color(0xFF9A3412),
    'gradient1': Color(0xFFF97316),
    'gradient2': Color(0xFFDC2626),
  }),
  red('红色', Colors.red, {
    'accent': Color(0xFFDC2626),
    'light': Color(0xFFFECDD3),
    'dark': Color(0xFF991B1B),
    'gradient1': Color(0xFFEF4444),
    'gradient2': Color(0xFFB91C1C),
  }),
  teal('青色', Colors.teal, {
    'accent': Color(0xFF0D9488),
    'light': Color(0xFFCCFBF1),
    'dark': Color(0xFF134E4A),
    'gradient1': Color(0xFF14B8A6),
    'gradient2': Color(0xFF0F766E),
  }),
  pink('粉色', Colors.pink, {
    'accent': Color(0xFFEC4899),
    'light': Color(0xFFFCE7F3),
    'dark': Color(0xFF9D174D),
    'gradient1': Color(0xFFF472B6),
    'gradient2': Color(0xFFDB2777),
  }),
  indigo('靛蓝', Colors.indigo, {
    'accent': Color(0xFF4F46E5),
    'light': Color(0xFFE0E7FF),
    'dark': Color(0xFF312E81),
    'gradient1': Color(0xFF6366F1),
    'gradient2': Color(0xFF4338CA),
  });

  const ThemeColor(this.name, this.color, this.customColors);
  final String name;
  final Color color;
  final Map<String, Color> customColors;

  /// 获取自定义颜色
  Color getCustomColor(String key) {
    return customColors[key] ?? color;
  }
}

/// 应用主题配置类
class AppTheme {
  /// 获取 Material 亮色主题
  static ThemeData getLightTheme(ThemeColor themeColor) {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: themeColor.color,
      brightness: Brightness.light,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: colorScheme,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
      ),
      cardTheme: CardThemeData(
        color: colorScheme.surface,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          side: BorderSide(color: colorScheme.outline),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        filled: true,
        fillColor: colorScheme.surface,
      ),
    );
  }

  /// 获取 Material 暗色主题
  static ThemeData getDarkTheme(ThemeColor themeColor) {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: themeColor.color,
      brightness: Brightness.dark,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: colorScheme,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
      ),
      cardTheme: CardThemeData(
        color: colorScheme.surface,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          side: BorderSide(color: colorScheme.outline),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        filled: true,
        fillColor: colorScheme.surface,
      ),
    );
  }

  /// 获取 ShadcnUI 亮色主题
  static ShadThemeData getShadLightTheme(ThemeColor themeColor) {
    return ShadThemeData(
      brightness: Brightness.light,
      colorScheme: _getShadColorScheme(themeColor, Brightness.light),
    );
  }

  /// 获取 ShadcnUI 暗色主题
  static ShadThemeData getShadDarkTheme(ThemeColor themeColor) {
    return ShadThemeData(
      brightness: Brightness.dark,
      colorScheme: _getShadColorScheme(themeColor, Brightness.dark),
    );
  }

  /// 根据主题颜色和亮度获取 ShadcnUI 颜色方案
  static ShadColorScheme _getShadColorScheme(ThemeColor themeColor, Brightness brightness) {
    switch (themeColor) {
      case ThemeColor.blue:
        return brightness == Brightness.light
          ? const ShadBlueColorScheme.light()
          : const ShadBlueColorScheme.dark();
      case ThemeColor.green:
        return brightness == Brightness.light
          ? const ShadGreenColorScheme.light()
          : const ShadGreenColorScheme.dark();
      case ThemeColor.purple:
        return brightness == Brightness.light
          ? const ShadVioletColorScheme.light()
          : const ShadVioletColorScheme.dark();
      case ThemeColor.orange:
        return brightness == Brightness.light
          ? const ShadOrangeColorScheme.light()
          : const ShadOrangeColorScheme.dark();
      case ThemeColor.red:
        return brightness == Brightness.light
          ? const ShadRedColorScheme.light()
          : const ShadRedColorScheme.dark();
      case ThemeColor.teal:
        return brightness == Brightness.light
          ? const ShadSlateColorScheme.light()
          : const ShadSlateColorScheme.dark();
      case ThemeColor.pink:
        return brightness == Brightness.light
          ? const ShadRoseColorScheme.light()
          : const ShadRoseColorScheme.dark();
      case ThemeColor.indigo:
        return brightness == Brightness.light
          ? const ShadSlateColorScheme.light()
          : const ShadSlateColorScheme.dark();
    }
  }
}


