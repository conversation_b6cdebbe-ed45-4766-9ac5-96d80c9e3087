import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:faith/controllers/theme_controller.dart';
import 'package:faith/theme/theme.dart';
import 'package:get/get.dart';

class ThemeSettingsPage extends StatelessWidget {
  const ThemeSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeController>(
      builder: (themeController) => Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: AppBar(
          title: const Text('主题设置'),
          backgroundColor: Theme.of(context).colorScheme.surface,
          foregroundColor: Theme.of(context).colorScheme.onSurface,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 亮色/暗色模式切换
              ShadCard(
                title: const Text('显示模式'),
                description: const Text('选择亮色或暗色主题'),
                child: Column(
                  children: [
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        const Text('暗色模式'),
                        const Spacer(),
                        ShadSwitch(
                          value: themeController.isDarkMode,
                          onChanged: (value) {
                            themeController.setTheme(value);
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ShadButton.outline(
                            onPressed: () => themeController.setTheme(false),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.light_mode,
                                  color: !themeController.isDarkMode 
                                    ? Theme.of(context).colorScheme.primary 
                                    : null,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '亮色',
                                  style: TextStyle(
                                    color: !themeController.isDarkMode 
                                      ? Theme.of(context).colorScheme.primary 
                                      : null,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ShadButton.outline(
                            onPressed: () => themeController.setTheme(true),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.dark_mode,
                                  color: themeController.isDarkMode 
                                    ? Theme.of(context).colorScheme.primary 
                                    : null,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '暗色',
                                  style: TextStyle(
                                    color: themeController.isDarkMode 
                                      ? Theme.of(context).colorScheme.primary 
                                      : null,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              
              // 颜色主题选择
              ShadCard(
                title: const Text('颜色主题'),
                description: const Text('选择您喜欢的颜色主题'),
                child: Column(
                  children: [
                    const SizedBox(height: 16),
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                        childAspectRatio: 1,
                      ),
                      itemCount: ThemeColor.values.length,
                      itemBuilder: (context, index) {
                        final themeColor = ThemeColor.values[index];
                        final isSelected = themeController.currentThemeColor == themeColor;
                        
                        return GestureDetector(
                          onTap: () {
                            themeController.setThemeColor(themeColor);
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('已切换到${themeColor.name}主题'),
                                duration: const Duration(seconds: 1),
                              ),
                            );
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: themeColor.color,
                              borderRadius: BorderRadius.circular(12),
                              border: isSelected 
                                ? Border.all(
                                    color: Theme.of(context).colorScheme.primary,
                                    width: 3,
                                  )
                                : null,
                              boxShadow: [
                                BoxShadow(
                                  color: themeColor.color.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: isSelected
                              ? const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 24,
                                )
                              : null,
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    
                    // 颜色主题名称显示
                    Text(
                      '当前主题: ${themeController.currentThemeColor.name}',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              
              // 主题预览
              ShadCard(
                title: const Text('主题预览'),
                description: const Text('查看当前主题的效果'),
                child: Column(
                  children: [
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ShadButton(
                            onPressed: () {},
                            child: const Text('主要按钮'),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ShadButton.outline(
                            onPressed: () {},
                            child: const Text('次要按钮'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const ShadInput(
                      placeholder: Text('输入框预览'),
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      children: [
                        ShadBadge(child: const Text('Primary')),
                        ShadBadge.secondary(child: const Text('Secondary')),
                        ShadBadge.destructive(child: const Text('Destructive')),
                        ShadBadge.outline(child: const Text('Outline')),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
