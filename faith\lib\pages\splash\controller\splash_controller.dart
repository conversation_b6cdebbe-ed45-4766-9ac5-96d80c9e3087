import 'package:get/get.dart';
import '../services/splash_service.dart';

/// Splash 页面控制器
/// 负责处理启动页面的逻辑和导航
class SplashController extends GetxController {
  /// 动画控制状态
  final _isAnimationComplete = false.obs;

  /// 获取动画完成状态
  bool get isAnimationComplete => _isAnimationComplete.value;

  /// 获取 Splash 服务
  SplashService get _splashService => SplashService.to;

  @override
  void onInit() {
    super.onInit();
    _startSplashSequence();
  }

  /// 启动 Splash 序列
  void _startSplashSequence() async {
    // 等待配置加载完成
    while (_splashService.isLoading) {
      await Future.delayed(const Duration(milliseconds: 100));
    }

    // 等待动画完成
    await Future.delayed(const Duration(milliseconds: 2000));
    _isAnimationComplete.value = true;

    // 根据配置的显示时长等待
    final remainingTime = (_splashService.config.displayDuration * 1000) - 2000;
    if (remainingTime > 0) {
      await Future.delayed(Duration(milliseconds: remainingTime));
    }

    // 导航到主页
    _navigateToHome();
  }

  /// 导航到主页
  void _navigateToHome() {
    Get.offAllNamed('/home');
  }

  /// 手动跳过 Splash
  void skipSplash() {
    if (_splashService.config.showSkipButton) {
      Get.offAllNamed('/home');
    }
  }
}
