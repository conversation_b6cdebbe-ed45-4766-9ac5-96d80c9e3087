import 'package:faith/router/pages.dart';
import 'package:faith/theme/theme_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

Future<void> main() async {
  await _initializeApp();

  // 自定义全局错误
  ErrorWidget.builder = (FlutterErrorDetails flutterErrorDetails) {
    debugPrint(flutterErrorDetails.toString());
    return const Material(
      child: Center(
          child: Text(
        "发生了没有处理的错误\n请通知开发者",
        textAlign: TextAlign.center,
      )),
    );
  };

  // 锁定屏幕方向
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown
  ]).then((_) async {
    runApp(const MainAppPage());
  });

}

Future<void> _initializeApp() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化 GetStorage
  await GetStorage.init();

  // 初始化主题控制器
  Get.put(ThemeController());
}

class MainAppPage extends StatelessWidget {
  const MainAppPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true, // 字体大小根据宽度自适应
      splitScreenMode: true, // 允许分屏模式
      child: RefreshConfiguration(
        headerBuilder: () => const MaterialClassicHeader(
          backgroundColor: Colors.transparent,
          color: Colors.blue,
        ), // 自定义头部刷新组件
        footerBuilder: () => const ClassicFooter(), // 自定义底部加载组件
        child: GetBuilder<ThemeController>(
          builder: (themeController) => ShadApp.custom(
            themeMode: themeController.themeMode,
            theme: themeController.shadLightTheme,
            darkTheme: themeController.shadDarkTheme,
            appBuilder: (context) {
              return GetMaterialApp(
                debugShowCheckedModeBanner: false,
                title: 'Faith',
                unknownRoute: Routes.unknownRoute, // 未知路由
                getPages: Routes.getPages, // 路由列表
                initialRoute: '/home', // 初始路由
                theme: Theme.of(context), // 使用 ShadApp 提供的主题
                builder: (context, child) {
                  return ShadAppBuilder(
                    child: Stack(
                      children: [
                        child!,
                      ],
                    ),
                  );
                }, // 全局配置
              );
            },
          ),
        ), // 使用ShadcnUI主题
      ),
    );
  }
}