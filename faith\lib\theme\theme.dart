import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

/// 主题颜色枚举
enum ThemeColor {
  blue('蓝色', Colors.blue),
  green('绿色', Colors.green),
  purple('紫色', Colors.purple),
  orange('橙色', Colors.orange),
  red('红色', Colors.red),
  teal('青色', Colors.teal),
  pink('粉色', Colors.pink),
  indigo('靛蓝', Colors.indigo);

  const ThemeColor(this.name, this.color);
  final String name;
  final Color color;
}

/// 应用主题配置类
class AppTheme {
  /// 获取 Material 亮色主题
  static ThemeData getLightTheme(ThemeColor themeColor) {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: themeColor.color,
      brightness: Brightness.light,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: colorScheme,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
      ),
      cardTheme: CardThemeData(
        color: colorScheme.surface,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          side: BorderSide(color: colorScheme.outline),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        filled: true,
        fillColor: colorScheme.surface,
      ),
    );
  }

  /// 获取 Material 暗色主题
  static ThemeData getDarkTheme(ThemeColor themeColor) {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: themeColor.color,
      brightness: Brightness.dark,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: colorScheme,
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
      ),
      cardTheme: CardThemeData(
        color: colorScheme.surface,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          side: BorderSide(color: colorScheme.outline),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        filled: true,
        fillColor: colorScheme.surface,
      ),
    );
  }

  /// 获取 ShadcnUI 亮色主题
  static ShadThemeData getShadLightTheme(ThemeColor themeColor) {
    return ShadThemeData(
      brightness: Brightness.light,
      colorScheme: _getShadColorScheme(themeColor, Brightness.light),
    );
  }

  /// 获取 ShadcnUI 暗色主题
  static ShadThemeData getShadDarkTheme(ThemeColor themeColor) {
    return ShadThemeData(
      brightness: Brightness.dark,
      colorScheme: _getShadColorScheme(themeColor, Brightness.dark),
    );
  }

  /// 根据主题颜色和亮度获取 ShadcnUI 颜色方案
  static ShadColorScheme _getShadColorScheme(ThemeColor themeColor, Brightness brightness) {
    switch (themeColor) {
      case ThemeColor.blue:
        return brightness == Brightness.light
          ? const ShadBlueColorScheme.light()
          : const ShadBlueColorScheme.dark();
      case ThemeColor.green:
        return brightness == Brightness.light
          ? const ShadGreenColorScheme.light()
          : const ShadGreenColorScheme.dark();
      case ThemeColor.purple:
        return brightness == Brightness.light
          ? const ShadVioletColorScheme.light()
          : const ShadVioletColorScheme.dark();
      case ThemeColor.orange:
        return brightness == Brightness.light
          ? const ShadOrangeColorScheme.light()
          : const ShadOrangeColorScheme.dark();
      case ThemeColor.red:
        return brightness == Brightness.light
          ? const ShadRedColorScheme.light()
          : const ShadRedColorScheme.dark();
      case ThemeColor.teal:
        return brightness == Brightness.light
          ? const ShadSlateColorScheme.light()
          : const ShadSlateColorScheme.dark();
      case ThemeColor.pink:
        return brightness == Brightness.light
          ? const ShadRoseColorScheme.light()
          : const ShadRoseColorScheme.dark();
      case ThemeColor.indigo:
        return brightness == Brightness.light
          ? const ShadSlateColorScheme.light()
          : const ShadSlateColorScheme.dark();
    }
  }
}
