import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:get/get.dart';
import 'package:faith/theme/theme_extensions.dart';
import '../controller/splash_controller.dart';

/// 支持背景图的 Splash 页面
/// 可以从后台配置应用名称和背景图片
class SplashPageWithBg extends StatefulWidget {
  /// 应用名称（可从后台配置）
  final String? appName;
  
  /// 背景图片 URL（可从后台配置）
  final String? backgroundImageUrl;
  
  /// 背景图片资源路径（本地资源）
  final String? backgroundImageAsset;
  
  /// 副标题（可从后台配置）
  final String? subtitle;

  const SplashPageWithBg({
    super.key,
    this.appName,
    this.backgroundImageUrl,
    this.backgroundImageAsset,
    this.subtitle,
  });

  @override
  State<SplashPageWithBg> createState() => _SplashPageWithBgState();
}

class _SplashPageWithBgState extends State<SplashPageWithBg>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _slideController;
  
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  final SplashController _controller = Get.put(SplashController());

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startAnimations();
  }

  /// 初始化动画控制器
  void _initAnimations() {
    // 淡入动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // 缩放动画
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // 滑动动画
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1400),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
  }

  /// 启动动画序列
  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _fadeController.forward();
    
    await Future.delayed(const Duration(milliseconds: 200));
    _scaleController.forward();
    
    await Future.delayed(const Duration(milliseconds: 100));
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // 背景图片或渐变
          _buildBackground(),
          
          // 遮罩层
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withValues(alpha: 0.3),
                  Colors.black.withValues(alpha: 0.1),
                  Colors.black.withValues(alpha: 0.4),
                ],
              ),
            ),
          ),
          
          // 主要内容
          SafeArea(
            child: Stack(
              children: [
                // 中心内容
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Logo 和标题区域
                      AnimatedBuilder(
                        animation: _fadeAnimation,
                        builder: (context, child) => FadeTransition(
                          opacity: _fadeAnimation,
                          child: ScaleTransition(
                            scale: _scaleAnimation,
                            child: Column(
                              children: [
                                // Logo 容器
                                Container(
                                  width: 120,
                                  height: 120,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        AppThemeExtensions.primary,
                                        AppThemeExtensions.accent,
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(30),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.3),
                                        blurRadius: 20,
                                        offset: const Offset(0, 10),
                                      ),
                                    ],
                                  ),
                                  child: const Icon(
                                    Icons.auto_awesome,
                                    size: 60,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 32),
                                
                                // 应用名称（支持从后台配置）
                                Text(
                                  widget.appName ?? 'Faith',
                                  style: const TextStyle(
                                    fontSize: 48,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    letterSpacing: 2,
                                    shadows: [
                                      Shadow(
                                        color: Colors.black54,
                                        blurRadius: 10,
                                        offset: Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 8),
                                
                                // 副标题（支持从后台配置）
                                Text(
                                  widget.subtitle ?? '简约 · 优雅 · 现代',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: Colors.white70,
                                    letterSpacing: 4,
                                    shadows: [
                                      Shadow(
                                        color: Colors.black54,
                                        blurRadius: 8,
                                        offset: Offset(0, 1),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 80),
                      
                      // 加载指示器
                      SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: _buildLoadingIndicator(),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 跳过按钮
                Positioned(
                  top: 20,
                  right: 20,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: ShadButton.ghost(
                      onPressed: _controller.skipSplash,
                      child: const Text(
                        '跳过',
                        style: TextStyle(
                          color: Colors.white70,
                        ),
                      ),
                    ),
                  ),
                ),
                
                // 底部信息
                Positioned(
                  bottom: 40,
                  left: 0,
                  right: 0,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Column(
                      children: [
                        // 版本信息
                        const Text(
                          'Version 1.0.0',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white60,
                          ),
                        ),
                        const SizedBox(height: 8),
                        
                        // 版权信息
                        Text(
                          '© 2024 ${widget.appName ?? 'Faith'} App',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white60,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建背景
  Widget _buildBackground() {
    // 如果有网络图片，优先使用网络图片
    if (widget.backgroundImageUrl != null && widget.backgroundImageUrl!.isNotEmpty) {
      return Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: NetworkImage(widget.backgroundImageUrl!),
            fit: BoxFit.cover,
            onError: (exception, stackTrace) {
              // 网络图片加载失败时的处理
              debugPrint('背景图片加载失败: $exception');
            },
          ),
        ),
      );
    }
    
    // 如果有本地资源图片，使用本地图片
    if (widget.backgroundImageAsset != null && widget.backgroundImageAsset!.isNotEmpty) {
      return Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(widget.backgroundImageAsset!),
            fit: BoxFit.cover,
          ),
        ),
      );
    }
    
    // 默认渐变背景
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppThemeExtensions.primary,
            AppThemeExtensions.accent,
            AppThemeExtensions.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
    );
  }

  /// 构建加载指示器
  Widget _buildLoadingIndicator() {
    return Column(
      children: [
        // 简约的点状加载指示器
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(3, (index) {
            return AnimatedBuilder(
              animation: _slideController,
              builder: (context, child) {
                final delay = index * 0.2;
                final animationValue = (_slideController.value + delay) % 1.0;
                final opacity = 0.3 + 0.7 * (0.5 + 0.5 * 
                    (animationValue < 0.5 
                        ? animationValue * 2 
                        : (1 - animationValue) * 2));
                
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: opacity),
                    shape: BoxShape.circle,
                  ),
                );
              },
            );
          }),
        ),
        const SizedBox(height: 24),
        
        // 加载文字
        const Text(
          '正在启动...',
          style: TextStyle(
            fontSize: 14,
            color: Colors.white70,
            letterSpacing: 1,
          ),
        ),
      ],
    );
  }
}
