{"buildFiles": ["D:\\environment\\Flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\environment\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\code\\cool-uniapp\\faith\\build\\.cxx\\Debug\\444d1x4m\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\environment\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\code\\cool-uniapp\\faith\\build\\.cxx\\Debug\\444d1x4m\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}