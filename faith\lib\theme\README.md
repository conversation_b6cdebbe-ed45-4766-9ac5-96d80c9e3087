# Faith 主题系统使用指南

## 概述

Faith 应用提供了一个强大的主题系统，支持：
- 亮色/暗色模式切换
- 8种预定义颜色主题（蓝色、绿色、紫色、橙色、红色、青色、粉色、靛蓝）
- 每个颜色主题包含多个自定义颜色变体
- 便捷的颜色访问方法

## 主题颜色结构

每个主题颜色包含以下颜色变体：
- `accent`: 强调色
- `light`: 浅色
- `dark`: 深色
- `gradient1`: 渐变色1
- `gradient2`: 渐变色2

## 使用方法

### 方法1：使用 BuildContext 扩展（推荐）

```dart
import 'package:faith/theme/theme_extension.dart';

// 在 Widget 的 build 方法中
Container(
  color: context.accentColor,        // 强调色
  child: Text(
    'Hello',
    style: TextStyle(color: context.darkColor), // 深色
  ),
)

// 其他可用属性
context.accentColor     // 强调色
context.lightColor      // 浅色
context.darkColor       // 深色
context.primaryColor    // 主色
context.themeGradient   // 主题渐变
context.shadowColor()   // 阴影颜色（可设置透明度）
context.borderColor()   // 边框颜色（可设置透明度）
```

### 方法2：使用静态方法

```dart
import 'package:faith/theme/theme_extension.dart';

// 获取自定义颜色
Color accentColor = AppThemeExtension.accent;
Color customColor = AppThemeExtension.getCustomColor('light');

// 创建自定义渐变
LinearGradient gradient = AppThemeExtension.createGradient(
  startColorKey: 'light',
  endColorKey: 'dark',
  begin: Alignment.topLeft,
  end: Alignment.bottomRight,
);

// 根据亮暗模式返回不同颜色
Color adaptiveColor = AppThemeExtension.adaptive(
  light: Colors.black,
  dark: Colors.white,
);
```

### 方法3：直接访问主题颜色

```dart
import 'package:faith/controllers/theme_controller.dart';

// 获取当前主题颜色
ThemeColor currentTheme = ThemeController.to.currentThemeColor;
Color customColor = currentTheme.getCustomColor('accent');
```

## 实际应用示例

### 自定义按钮

```dart
ShadButton(
  onPressed: () {},
  style: ShadButtonStyle(
    backgroundColor: context.accentColor,
    foregroundColor: Colors.white,
  ),
  child: const Text('自定义按钮'),
)
```

### 自定义卡片

```dart
Container(
  padding: const EdgeInsets.all(16),
  decoration: BoxDecoration(
    color: context.lightColor,
    borderRadius: BorderRadius.circular(12),
    border: Border.all(color: context.borderColor()),
    boxShadow: [
      BoxShadow(
        color: context.shadowColor(),
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ],
  ),
  child: Text(
    '自定义卡片',
    style: TextStyle(color: context.darkColor),
  ),
)
```

### 渐变背景

```dart
Container(
  decoration: BoxDecoration(
    gradient: context.themeGradient,
    borderRadius: BorderRadius.circular(12),
  ),
  child: const Center(
    child: Text(
      '渐变背景',
      style: TextStyle(color: Colors.white),
    ),
  ),
)
```

### 自定义输入框

```dart
Container(
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(8),
    border: Border.all(color: context.accentColor, width: 2),
  ),
  child: TextField(
    decoration: InputDecoration(
      hintText: '自定义输入框',
      border: InputBorder.none,
      contentPadding: const EdgeInsets.all(16),
      hintStyle: TextStyle(
        color: context.accentColor.withValues(alpha: 0.6),
      ),
    ),
  ),
)
```

## 主题切换

```dart
import 'package:faith/controllers/theme_controller.dart';

// 切换亮色/暗色模式
ThemeController.to.toggleTheme();

// 设置特定模式
ThemeController.to.setTheme(true);  // 暗色模式
ThemeController.to.setTheme(false); // 亮色模式

// 设置颜色主题
ThemeController.to.setThemeColor(ThemeColor.blue);
ThemeController.to.setThemeColor(ThemeColor.purple);
```

## 注意事项

1. 确保在使用扩展方法前导入 `theme_extension.dart`
2. 颜色会根据当前选择的主题自动更新
3. 所有颜色设置都会自动保存到本地存储
4. 建议优先使用 BuildContext 扩展方法，代码更简洁
5. 在需要响应主题变化的 Widget 中使用 `GetBuilder<ThemeController>`

## 可用的颜色主题

- 蓝色 (ThemeColor.blue)
- 绿色 (ThemeColor.green)
- 紫色 (ThemeColor.purple)
- 橙色 (ThemeColor.orange)
- 红色 (ThemeColor.red)
- 青色 (ThemeColor.teal)
- 粉色 (ThemeColor.pink)
- 靛蓝 (ThemeColor.indigo)

每个主题都有对应的亮色和暗色版本，会根据当前的显示模式自动切换。
