{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/environment/Android/SDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/environment/Android/SDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/environment/Android/SDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/environment/Android/SDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-ae04008ff808a046a420.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-a1ca1063b43c6e9ad7e4.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-05600faeb6781f15176e.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-a1ca1063b43c6e9ad7e4.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-05600faeb6781f15176e.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-ae04008ff808a046a420.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}