import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:faith/theme/theme_extension.dart';
import 'package:faith/controllers/theme_controller.dart';
import 'package:get/get.dart';

class CustomColorDemoPage extends StatelessWidget {
  const CustomColorDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ThemeController>(
      builder: (themeController) => Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: AppBar(
          title: const Text('自定义颜色演示'),
          backgroundColor: context.accentColor, // 使用扩展方法
          foregroundColor: Colors.white,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                '自定义颜色使用示例',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: context.accentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              
              // 方法1：使用扩展方法
              _buildSection(
                context,
                '方法1：使用 BuildContext 扩展',
                [
                  _buildColorCard(
                    '强调色 (context.accentColor)',
                    context.accentColor,
                    'context.accentColor',
                  ),
                  _buildColorCard(
                    '浅色 (context.lightColor)',
                    context.lightColor,
                    'context.lightColor',
                  ),
                  _buildColorCard(
                    '深色 (context.darkColor)',
                    context.darkColor,
                    'context.darkColor',
                  ),
                  _buildColorCard(
                    '主色 (context.primaryColor)',
                    context.primaryColor,
                    'context.primaryColor',
                  ),
                ],
              ),
              
              // 方法2：使用静态方法
              _buildSection(
                context,
                '方法2：使用 AppThemeExtension 静态方法',
                [
                  _buildColorCard(
                    '渐变色1 (AppThemeExtension.gradient1)',
                    AppThemeExtension.gradient1,
                    'AppThemeExtension.gradient1',
                  ),
                  _buildColorCard(
                    '渐变色2 (AppThemeExtension.gradient2)',
                    AppThemeExtension.gradient2,
                    'AppThemeExtension.gradient2',
                  ),
                  _buildColorCard(
                    '自定义颜色 (getCustomColor)',
                    AppThemeExtension.getCustomColor('accent'),
                    'AppThemeExtension.getCustomColor("accent")',
                  ),
                ],
              ),
              
              // 方法3：渐变效果
              _buildGradientSection(context),
              
              // 方法4：实际应用示例
              _buildPracticalExamples(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        ...children,
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildColorCard(String title, Color color, String code) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 4),
                Text(
                  code,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGradientSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '方法3：渐变效果',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          height: 120,
          decoration: BoxDecoration(
            gradient: context.themeGradient, // 使用扩展方法获取渐变
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Center(
            child: Text(
              'context.themeGradient',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 120,
          decoration: BoxDecoration(
            gradient: AppThemeExtension.createGradient(
              startColorKey: 'light',
              endColorKey: 'dark',
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Center(
            child: Text(
              'AppThemeExtension.createGradient(...)',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildPracticalExamples(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '方法4：实际应用示例',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        
        // 自定义按钮
        ShadButton(
          onPressed: () {},
          style: ShadButtonStyle(
            backgroundColor: context.accentColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('使用强调色的按钮'),
        ),
        const SizedBox(height: 12),
        
        // 自定义卡片
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: context.lightColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: context.borderColor()),
            boxShadow: [
              BoxShadow(
                color: context.shadowColor(),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '自定义卡片',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: context.darkColor,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '这个卡片使用了自定义的浅色背景、边框颜色和阴影颜色',
                style: TextStyle(color: context.darkColor),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        
        // 自定义输入框
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: context.accentColor, width: 2),
          ),
          child: TextField(
            decoration: InputDecoration(
              hintText: '使用强调色边框的输入框',
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              hintStyle: TextStyle(color: context.accentColor.withValues(alpha: 0.6)),
            ),
          ),
        ),
      ],
    );
  }
}
