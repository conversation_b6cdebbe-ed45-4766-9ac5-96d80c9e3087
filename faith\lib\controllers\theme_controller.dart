import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class ThemeController extends GetxController {
  static ThemeController get to => Get.find();
  
  final _box = GetStorage();
  final _key = 'isDarkMode';
  
  // 响应式变量
  final _isDarkMode = false.obs;
  bool get isDarkMode => _isDarkMode.value;
  
  @override
  void onInit() {
    super.onInit();
    // 从本地存储读取主题设置
    _isDarkMode.value = _box.read(_key) ?? false;
  }
  
  /// 切换主题
  void toggleTheme() {
    _isDarkMode.value = !_isDarkMode.value;
    _box.write(_key, _isDarkMode.value);
    
    // 更新系统主题模式
    Get.changeThemeMode(_isDarkMode.value ? ThemeMode.dark : ThemeMode.light);
  }
  
  /// 设置主题
  void setTheme(bool isDark) {
    _isDarkMode.value = isDark;
    _box.write(_key, isDark);
    Get.changeThemeMode(isDark ? ThemeMode.dark : ThemeMode.light);
  }
  
  /// 获取当前主题模式
  ThemeMode get themeMode => _isDarkMode.value ? ThemeMode.dark : ThemeMode.light;
}
