import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

/// 自定义靛蓝色 ShadcnUI 颜色方案
/// 为靛蓝主题提供专门的颜色配置
class ShadIndigoColorScheme extends ShadColorScheme {
  /// 靛蓝亮色主题
  const ShadIndigoColorScheme.light()
      : super(
          // 背景色 - 纯白
          background: const Color(0xFFFFFFFF),
          // 前景色 - 深靛蓝
          foreground: const Color(0xFF1E1B4B),
          
          // 卡片背景 - 纯白
          card: const Color(0xFFFFFFFF),
          // 卡片前景 - 深靛蓝
          cardForeground: const Color(0xFF1E1B4B),
          
          // 弹出层背景 - 纯白
          popover: const Color(0xFFFFFFFF),
          // 弹出层前景 - 深靛蓝
          popoverForeground: const Color(0xFF1E1B4B),
          
          // 主色 - 靛蓝
          primary: const Color(0xFF4F46E5),
          // 主色前景 - 白色
          primaryForeground: const Color(0xFFFFFFFF),
          
          // 次要色 - 浅灰蓝
          secondary: const Color(0xFFF1F5F9),
          // 次要色前景 - 深靛蓝
          secondaryForeground: const Color(0xFF1E1B4B),
          
          // 静音色 - 浅灰蓝
          muted: const Color(0xFFF1F5F9),
          // 静音色前景 - 中性灰
          mutedForeground: const Color(0xFF64748B),
          
          // 强调色 - 浅靛蓝
          accent: const Color(0xFFE0E7FF),
          // 强调色前景 - 深靛蓝
          accentForeground: const Color(0xFF1E1B4B),
          
          // 破坏性操作色 - 红色
          destructive: const Color(0xFFEF4444),
          // 破坏性操作前景 - 白色
          destructiveForeground: const Color(0xFFFFFFFF),
          
          // 边框色 - 浅灰
          border: const Color(0xFFE2E8F0),
          // 输入框边框 - 浅灰
          input: const Color(0xFFE2E8F0),
          
          // 焦点环色 - 靛蓝
          ring: const Color(0xFF4F46E5),
          // 选中色 - 浅靛蓝
          selection: const Color(0xFFE0E7FF),
        );

  /// 靛蓝暗色主题
  const ShadIndigoColorScheme.dark()
      : super(
          // 背景色 - 深靛蓝
          background: const Color(0xFF0F0F23),
          // 前景色 - 浅色
          foreground: const Color(0xFFF8FAFC),
          
          // 卡片背景 - 深靛蓝变体
          card: const Color(0xFF1E1B4B),
          // 卡片前景 - 浅色
          cardForeground: const Color(0xFFF8FAFC),
          
          // 弹出层背景 - 深靛蓝变体
          popover: const Color(0xFF1E1B4B),
          // 弹出层前景 - 浅色
          popoverForeground: const Color(0xFFF8FAFC),
          
          // 主色 - 亮靛蓝
          primary: const Color(0xFF6366F1),
          // 主色前景 - 深靛蓝
          primaryForeground: const Color(0xFF1E1B4B),
          
          // 次要色 - 中等靛蓝
          secondary: const Color(0xFF312E81),
          // 次要色前景 - 浅色
          secondaryForeground: const Color(0xFFF8FAFC),
          
          // 静音色 - 中等靛蓝
          muted: const Color(0xFF312E81),
          // 静音色前景 - 中性浅色
          mutedForeground: const Color(0xFF94A3B8),
          
          // 强调色 - 深靛蓝
          accent: const Color(0xFF4338CA),
          // 强调色前景 - 浅色
          accentForeground: const Color(0xFFF8FAFC),
          
          // 破坏性操作色 - 暗红
          destructive: const Color(0xFF7F1D1D),
          // 破坏性操作前景 - 浅色
          destructiveForeground: const Color(0xFFF8FAFC),
          
          // 边框色 - 中等靛蓝
          border: const Color(0xFF312E81),
          // 输入框边框 - 中等靛蓝
          input: const Color(0xFF312E81),
          
          // 焦点环色 - 亮靛蓝
          ring: const Color(0xFF6366F1),
          // 选中色 - 深靛蓝
          selection: const Color(0xFF4338CA),
        );
}

/// 自定义青色 ShadcnUI 颜色方案
/// 为青色主题提供专门的颜色配置
class ShadTealColorScheme extends ShadColorScheme {
  /// 青色亮色主题
  const ShadTealColorScheme.light()
      : super(
          background: const Color(0xFFFFFFFF),
          foreground: const Color(0xFF0F172A),
          card: const Color(0xFFFFFFFF),
          cardForeground: const Color(0xFF0F172A),
          popover: const Color(0xFFFFFFFF),
          popoverForeground: const Color(0xFF0F172A),
          primary: const Color(0xFF0D9488),
          primaryForeground: const Color(0xFFFFFFFF),
          secondary: const Color(0xFFF0FDFA),
          secondaryForeground: const Color(0xFF0F172A),
          muted: const Color(0xFFF0FDFA),
          mutedForeground: const Color(0xFF64748B),
          accent: const Color(0xFFCCFBF1),
          accentForeground: const Color(0xFF0F172A),
          destructive: const Color(0xFFEF4444),
          destructiveForeground: const Color(0xFFFFFFFF),
          border: const Color(0xFFE2E8F0),
          input: const Color(0xFFE2E8F0),
          ring: const Color(0xFF0D9488),
          selection: const Color(0xFFCCFBF1),
        );

  /// 青色暗色主题
  const ShadTealColorScheme.dark()
      : super(
          background: const Color(0xFF0F172A),
          foreground: const Color(0xFFF8FAFC),
          card: const Color(0xFF134E4A),
          cardForeground: const Color(0xFFF8FAFC),
          popover: const Color(0xFF134E4A),
          popoverForeground: const Color(0xFFF8FAFC),
          primary: const Color(0xFF14B8A6),
          primaryForeground: const Color(0xFF042F2E),
          secondary: const Color(0xFF1E293B),
          secondaryForeground: const Color(0xFFF8FAFC),
          muted: const Color(0xFF1E293B),
          mutedForeground: const Color(0xFF94A3B8),
          accent: const Color(0xFF0F766E),
          accentForeground: const Color(0xFFF8FAFC),
          destructive: const Color(0xFF7F1D1D),
          destructiveForeground: const Color(0xFFF8FAFC),
          border: const Color(0xFF1E293B),
          input: const Color(0xFF1E293B),
          ring: const Color(0xFF14B8A6),
          selection: const Color(0xFF0F766E),
        );
}

/// 自定义颜色方案扩展
/// 为现有颜色方案添加额外的颜色属性
extension ShadColorSchemeExtension on ShadColorScheme {
  /// 成功色 - 绿色
  Color get success => const Color(0xFF10B981);
  
  /// 警告色 - 黄色
  Color get warning => const Color(0xFFF59E0B);
  
  /// 信息色 - 蓝色
  Color get info => const Color(0xFF3B82F6);
  
  /// 表面变体色
  Color get surfaceVariant => muted;
  
  /// 轮廓色
  Color get outline => border;
  
  /// 轮廓变体色（半透明）
  Color get outlineVariant => border.withValues(alpha: 0.5);
  
  /// 阴影色（半透明前景色）
  Color get shadow => foreground.withValues(alpha: 0.1);
  
  /// 表面色调色
  Color get surfaceTint => primary;
  
  /// 错误色（与 destructive 相同）
  Color get error => destructive;
  
  /// 错误前景色
  Color get onError => destructiveForeground;
}
