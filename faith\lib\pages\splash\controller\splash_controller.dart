import 'package:get/get.dart';

/// Splash 页面控制器
/// 负责处理启动页面的逻辑和导航
class SplashController extends GetxController {
  /// 动画控制状态
  final _isAnimationComplete = false.obs;
  
  /// 获取动画完成状态
  bool get isAnimationComplete => _isAnimationComplete.value;

  @override
  void onInit() {
    super.onInit();
    _startSplashSequence();
  }

  /// 启动 Splash 序列
  void _startSplashSequence() async {
    // 等待动画完成
    await Future.delayed(const Duration(milliseconds: 1500));
    _isAnimationComplete.value = true;
    
    // 再等待一段时间让用户看到完整的界面
    await Future.delayed(const Duration(milliseconds: 1000));
    
    // 导航到主页
    _navigateToHome();
  }

  /// 导航到主页
  void _navigateToHome() {
    Get.offAllNamed('/home');
  }

  /// 手动跳过 Splash
  void skipSplash() {
    Get.offAllNamed('/home');
  }
}
